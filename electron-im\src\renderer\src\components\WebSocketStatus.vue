<!-- WebSocket 连接状态提示组件 -->
<template>
  <div
    v-if="shouldShowStatus"
    class="py-1 mx-3 mb-2 rounded-md text-xs transition-all duration-300"
    :class="statusClasses"
  >
    <div class="flex items-center gap-2">
      <!-- 状态图标 -->
      <div class="flex-shrink-0">
        <div
          v-if="status === WebSocketState.CONNECTING || status === WebSocketState.RECONNECTING"
          class="p-1 ml-2 w-3 h-3 border-1 border-current border-t-transparent rounded-full animate-spin"
        ></div>
        <div v-else-if="status === WebSocketState.CONNECTED" class="p-1 ml-2">
          <svg class="w-3 h-3" viewBox="0 0 1024 1024">
            <path
              d="M510.585905 658.67581a33.353143 33.353143 0 1 1 47.152762-47.201524l22.186666 22.186666c37.546667 37.546667 37.546667 98.352762 0 135.850667L402.529524 946.95619a221.525333 221.525333 0 1 1-313.246476-313.246476l177.395809-177.395809a96.060952 96.060952 0 0 1 135.850667 0l22.186666 22.186666a33.353143 33.353143 0 1 1-47.201523 47.152762l-22.137905-22.186666a29.354667 29.354667 0 0 0-41.545143 0l-177.395809 177.395809a154.819048 154.819048 0 0 0 218.940952 218.940953l177.395809-177.39581a29.354667 29.354667 0 0 0 0-41.545143l-22.186666-22.137905z m135.899428-135.899429l22.137905 22.186667a29.354667 29.354667 0 0 0 41.545143 0l177.395809-177.39581a154.819048 154.819048 0 1 0-218.940952-218.940952l-177.395809 177.395809a29.354667 29.354667 0 0 0 0 41.545143l22.186666 22.137905a33.353143 33.353143 0 1 1-47.152762 47.201524l-22.186666-22.186667a96.060952 96.060952 0 0 1 0-135.850667l177.395809-177.395809a221.525333 221.525333 0 1 1 313.246476 313.246476l-177.395809 177.39581a96.060952 96.060952 0 0 1-135.850667 0l-22.186666-22.186667a33.353143 33.353143 0 1 1 47.201523-47.152762zM306.224762 681.301333l388.242286-388.242285a33.353143 33.353143 0 0 1 47.201523 47.152762L353.426286 728.502857a33.353143 33.353143 0 0 1-47.152762-47.201524z"
              fill="#008236"
            ></path>
          </svg>
        </div>
        <div v-else-if="status === WebSocketState.DISCONNECTED" class="p-1 ml-2">
          <svg class="w-3 h-3" viewBox="0 0 1024 1024">
            <path
              d="M510.585905 658.67581a33.353143 33.353143 0 1 1 47.152762-47.201524l22.186666 22.186666c37.546667 37.546667 37.546667 98.352762 0 135.850667L402.529524 946.95619a221.525333 221.525333 0 1 1-313.246476-313.246476l177.395809-177.395809a96.060952 96.060952 0 0 1 135.850667 0l22.186666 22.186666a33.353143 33.353143 0 1 1-47.201523 47.152762l-22.137905-22.186666a29.354667 29.354667 0 0 0-41.545143 0l-177.395809 177.395809a154.819048 154.819048 0 0 0 218.940952 218.940953l177.395809-177.39581a29.354667 29.354667 0 0 0 0-41.545143l-22.186666-22.137905z m135.899428-135.899429l22.137905 22.186667a29.354667 29.354667 0 0 0 41.545143 0l177.395809-177.39581a154.819048 154.819048 0 1 0-218.940952-218.940952l-177.395809 177.395809a29.354667 29.354667 0 0 0 0 41.545143l22.186666 22.137905a33.353143 33.353143 0 1 1-47.152762 47.201524l-22.186666-22.186667a96.060952 96.060952 0 0 1 0-135.850667l177.395809-177.395809a221.525333 221.525333 0 1 1 313.246476 313.246476l-177.395809 177.39581a96.060952 96.060952 0 0 1-135.850667 0l-22.186666-22.186667a33.353143 33.353143 0 1 1 47.201523-47.152762zM306.224762 681.301333l388.242286-388.242285a33.353143 33.353143 0 0 1 47.201523 47.152762L353.426286 728.502857a33.353143 33.353143 0 0 1-47.152762-47.201524z"
              fill="#d81e06"
            ></path>
          </svg>
        </div>
        <div v-else-if="status === WebSocketState.ERROR" class="p-1 ml-2">
          <svg class="w-3 h-3" viewBox="0 0 1024 1024">
            <path
              d="M510.585905 658.67581a33.353143 33.353143 0 1 1 47.152762-47.201524l22.186666 22.186666c37.546667 37.546667 37.546667 98.352762 0 135.850667L402.529524 946.95619a221.525333 221.525333 0 1 1-313.246476-313.246476l177.395809-177.395809a96.060952 96.060952 0 0 1 135.850667 0l22.186666 22.186666a33.353143 33.353143 0 1 1-47.201523 47.152762l-22.137905-22.186666a29.354667 29.354667 0 0 0-41.545143 0l-177.395809 177.395809a154.819048 154.819048 0 0 0 218.940952 218.940953l177.395809-177.39581a29.354667 29.354667 0 0 0 0-41.545143l-22.186666-22.137905z m135.899428-135.899429l22.137905 22.186667a29.354667 29.354667 0 0 0 41.545143 0l177.395809-177.39581a154.819048 154.819048 0 1 0-218.940952-218.940952l-177.395809 177.395809a29.354667 29.354667 0 0 0 0 41.545143l22.186666 22.137905a33.353143 33.353143 0 1 1-47.152762 47.201524l-22.186666-22.186667a96.060952 96.060952 0 0 1 0-135.850667l177.395809-177.395809a221.525333 221.525333 0 1 1 313.246476 313.246476l-177.395809 177.39581a96.060952 96.060952 0 0 1-135.850667 0l-22.186666-22.186667a33.353143 33.353143 0 1 1 47.201523-47.152762zM306.224762 681.301333l388.242286-388.242285a33.353143 33.353143 0 0 1 47.201523 47.152762L353.426286 728.502857a33.353143 33.353143 0 0 1-47.152762-47.201524z"
              fill="#d81e06"
            ></path>
          </svg>
        </div>
      </div>

      <!-- 状态文本 -->
      <span class="flex-1">{{ statusText }}</span>

      <!-- 关闭按钮 (仅在连接成功时显示) -->
      <button
        v-if="status === WebSocketState.CONNECTED && canClose"
        @click="handleClose"
        class="flex-shrink-0 ml-2 p-1 rounded hover:bg-black hover:bg-opacity-10 transition-colors"
        title="关闭提示"
      >
        <svg class="w-3 h-3" viewBox="0 0 1024 1024" fill="currentColor">
          <path
            d="M562.9 517.7l173.1 173c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0l-173-173.1c-3.1-3.1-8.2-3.1-11.3 0L333.3 736c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l173.1-173.1c3.1-3.1 3.1-8.2 0-11.3L288 333.3c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l173.1 173.1c3.1 3.1 8.2 3.1 11.3 0l173-173.1c12.5-12.5 32.8-12.5 45.3 0s12.5 32.8 0 45.3l-173.1 173a8.15 8.15 0 0 0 0 11.4z"
          ></path>
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useWebSocketStore } from '../store/websocket'
import { wsManager, WebSocketState } from '../services/websocket'

// 使用全局 WebSocket 状态
const webSocketStore = useWebSocketStore()

// 使用 storeToRefs 保持响应性
const {
  state: status,
  reconnectAttempts,
  statusText,
  statusClasses,
  shouldShowStatus,
  canClose,
  isManuallyHidden
} = storeToRefs(webSocketStore)

// 获取 actions（不需要响应性）
const { setManuallyHidden } = webSocketStore

// 简化的方法
const handleClose = () => {
  console.log('WebSocketStatus - 用户点击关闭按钮')
  setManuallyHidden(true)
}

// 状态同步处理
const handleStateChange = (newState: WebSocketState) => {
  console.log('WebSocketStatus - 收到状态变化事件:', newState)
  // 强制同步状态到全局 store
  const connectionInfo = wsManager.getConnectionInfo()
  webSocketStore.setConnectionInfo(connectionInfo)
}

// 初始化同步（确保组件挂载时状态正确）
const initializeStatus = () => {
  console.log('WebSocketStatus - 初始化状态同步开始')

  // 先检查当前状态
  console.log('WebSocketStatus - 同步前状态:', {
    wsManagerState: wsManager.getState(),
    wsManagerConnectionInfo: wsManager.getConnectionInfo(),
    storeState: status.value,
    storeReconnectAttempts: reconnectAttempts.value,
    storeStateType: typeof status.value
  })

  try {
    // 获取当前连接信息并同步到store
    const connectionInfo = wsManager.getConnectionInfo()
    webSocketStore.setConnectionInfo(connectionInfo)

    // 延迟检查同步结果（不打印日志）
    setTimeout(() => {
      // 状态同步完成，但不打印日志
    }, 50)
  } catch (error) {
    console.warn('WebSocketStatus - 初始化状态同步失败:', error)
  }
}

// 手动状态同步（仅在需要时调用）
const manualSyncStatus = () => {
  const managerState = wsManager.getState()
  const currentStoreState = status.value

  if (managerState !== currentStoreState) {
    console.log('WebSocketStatus - 手动同步状态:', {
      managerState: managerState,
      storeState: currentStoreState
    })
    const connectionInfo = wsManager.getConnectionInfo()
    webSocketStore.setConnectionInfo(connectionInfo)
  }
}

// 生命周期
onMounted(() => {
  console.log('WebSocketStatus - 组件挂载，开始初始化')

  // 检查响应式状态是否正常
  console.log('WebSocketStatus - 响应式状态检查:', {
    statusValue: status.value,
    webSocketStoreStateValue: webSocketStore.state.value
  })

  // 立即同步状态
  initializeStatus()

  // 监听 WebSocket 状态变化事件
  wsManager.on('onStateChange', handleStateChange)

  // 延迟再次检查状态
  setTimeout(() => {
    console.log('WebSocketStatus - 延迟状态检查:', {
      status: status.value,
      reconnectAttempts: reconnectAttempts.value,
      shouldShow: shouldShowStatus.value,
      webSocketStoreState: webSocketStore.state.value
    })
  }, 1000)
})

onUnmounted(() => {
  console.log('WebSocketStatus - 组件卸载，清理资源')

  // 移除事件监听
  wsManager.off('onStateChange', handleStateChange)
})
</script>

<style scoped>
/* 动画效果 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 旋转动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
