const isDevelopment = process.env.NODE_ENV === 'development'
// 客户端配置
export const API_CONFIG = {
  // 使用相对路径，通过代理转发到实际的API服务器
  BASE_URL: isDevelopment ? '/zhuyuqian' : 'http://10.213.20.4:3000',
  WS_URL: isDevelopment ? '/zhuyuqian/ws' : 'ws://10.213.20.4:3000/ws',
  ENDPOINTS: {
    LOGIN: '/api/auth/login',
    USERS: '/api/users'
  },
  TIMEOUT: 10000,
  // 是否使用模拟数据
  USE_MOCK: false
}

export const APP_CONFIG = {
  TOKEN_KEY: 'auth_token',
  USER_KEY: 'user_info'
}
