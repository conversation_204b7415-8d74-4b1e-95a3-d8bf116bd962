// WebSocket 统一类型定义
export enum MessageType {
  HEARTBEAT = 0,
  TEXT_MESSAGE = 1,
  ERROR = 100
}

// WebSocket 连接状态
export enum WebSocketState {
  CONNECTING = 'CONNECTING',
  CONNECTED = 'CONNECTED',
  DISCONNECTED = 'DISCONNECTED',
  RECONNECTING = 'RECONNECTING',
  ERROR = 'ERROR'
}

// 发送消息格式 - 匹配 protobuf IMMessage 结构
export interface SendTextMessage {
  type: MessageType.TEXT_MESSAGE
  timestamp: number
  token?: string
  textMessage: {
    receiverId: string
    content: string
  }
}

export interface SendHeartbeat {
  type: MessageType.HEARTBEAT
  timestamp: number
  token?: string
}

// 接收消息格式 - 匹配实际服务器返回的消息结构
export interface ReceivedTextMessage {
  type: MessageType.TEXT_MESSAGE
  messageId: string
  timestamp: string | number // 支持字符串和数字格式
  token?: string
  textMessage: {
    id: string
    senderId: string
    receiverId: string
    content: string
    type: number
    timestamp: string | number
    status: string
    note: string
    sender: {
      username: string
      userId: string
    }
  }
}

export interface ReceivedErrorMessage {
  type: MessageType.ERROR
  messageId: string
  timestamp: string | number // 支持字符串和数字格式
  token?: string
  code: string
  message: string
}

export interface ReceivedHeartbeat {
  type: MessageType.HEARTBEAT
  messageId: string
  timestamp: string | number // 支持字符串和数字格式
  token?: string
}

// 联合类型
export type SendMessage = SendTextMessage | SendHeartbeat
export type ReceivedMessage = ReceivedTextMessage | ReceivedErrorMessage | ReceivedHeartbeat

// 事件回调用的消息类型
export interface TextMessage {
  id: string
  senderId: string
  receiverId: string
  content: string
  timestamp: number
  status?: string // 消息状态
  note?: string // 备注信息
  sender?: {
    username: string
    userId: string
  }
}

export interface SystemMessage {
  id: string
  content: string
  timestamp: number
}

export interface ErrorMessage {
  code: string
  message: string
}

// WebSocket 服务层事件类型
export interface WebSocketServiceEvents {
  onOpen: () => void
  onClose: (event: CloseEvent) => void
  onError: (error: Event) => void
  onMessage: (message: ReceivedMessage) => void
}

// WebSocket 管理层事件类型
export interface WebSocketManagerEvents {
  onStateChange: (state: WebSocketState) => void
  onMessage: (message: TextMessage) => void
  onSystemMessage: (message: SystemMessage) => void
  onError: (error: ErrorMessage) => void
  onHeartbeat: () => void
}

// 连接配置
export interface WebSocketConfig {
  url: string
  heartbeatInterval: number
  reconnectDelay: number
  maxReconnectAttempts: number
  connectionTimeout: number
  heartbeatTimeout: number
  maxMissedHeartbeats: number
}

// 连接状态信息
export interface ConnectionInfo {
  isConnected: boolean
  state: WebSocketState
  reconnectAttempts: number
  hasEverReconnected: boolean
  lastConnectedTime?: Date
  lastDisconnectedTime?: Date
}

// 心跳状态信息
export interface HeartbeatStatus {
  lastSent: number
  lastReceived: number
  missedCount: number
  maxMissed: number
  isTimeout: boolean
}

// 连接健康状态
export interface ConnectionHealth {
  isConnected: boolean
  isHealthy: boolean
  timeSinceLastHeartbeat: number
  missedHeartbeats: number
  status: 'healthy' | 'warning' | 'unhealthy' | 'disconnected' | 'unknown'
}

// WebSocket 服务接口
export interface IWebSocketService {
  connect(token?: string): Promise<void>
  disconnect(): void
  send(message: SendMessage): boolean
  isConnected(): boolean
  getConnectionHealth(): ConnectionHealth
  getHeartbeatStatus(): HeartbeatStatus
  on<K extends keyof WebSocketServiceEvents>(event: K, callback: WebSocketServiceEvents[K]): void
  off<K extends keyof WebSocketServiceEvents>(event: K): void
}

// WebSocket 管理器接口
export interface IWebSocketManager {
  connect(token?: string): Promise<void>
  disconnect(): void
  reconnect(): void
  sendTextMessage(receiverId: string, content: string): boolean
  isConnected(): boolean
  getState(): WebSocketState
  getConnectionInfo(): ConnectionInfo
  on<K extends keyof WebSocketManagerEvents>(event: K, callback: WebSocketManagerEvents[K]): void
  off<K extends keyof WebSocketManagerEvents>(event: K): void
  cleanup(): void
}

// Store 状态接口
export interface WebSocketStoreState {
  state: WebSocketState
  reconnectAttempts: number
  hasEverReconnected: boolean
  isManuallyHidden: boolean
  lastStateChangeTime: Date
  connectionInfo?: ConnectionInfo
}

// Store Actions 接口
export interface WebSocketStoreActions {
  setState(state: WebSocketState): void
  setReconnectAttempts(attempts: number): void
  setHasEverReconnected(value: boolean): void
  setManuallyHidden(value: boolean): void
  setConnectionInfo(info: ConnectionInfo): void
  reset(): void
}

// 事件发射器类型
export type EventCallback<T = any> = (data: T) => void
export type EventMap = Record<string, EventCallback>

// 默认配置
export const DEFAULT_WEBSOCKET_CONFIG: WebSocketConfig = {
  url: '',
  heartbeatInterval: 29000, // 29秒
  reconnectDelay: 3000, // 3秒
  maxReconnectAttempts: 5,
  connectionTimeout: 10000, // 10秒
  heartbeatTimeout: 35000, // 35秒
  maxMissedHeartbeats: 2
}
