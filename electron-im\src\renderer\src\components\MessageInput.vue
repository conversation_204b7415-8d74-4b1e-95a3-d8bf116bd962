<!-- 消息输入组件 -->
<template>
  <div class="border-t border-gray-300 bg-[#ebeff5] p-4 min-h-40 flex-shrink-0">
    <div class="flex items-center justify-between mb-1">
      <div class="flex items-center gap-2">
        <button
          class="bg-transparent border-none cursor-pointer p-2 rounded text-gray-500 text-base hover:bg-gray-100"
          @click="$emit('insert-emoji')"
        >
          <img src="../assets/editor/insert-emoji.svg" alt="表情" class="w-5 h-5" />
        </button>
        <button
          class="bg-transparent border-none cursor-pointer p-2 rounded text-gray-500 text-base hover:bg-gray-100"
          @click="$emit('screen-shot')"
        >
          <img src="../assets/editor/screenshot.svg" alt="截图" class="w-5 h-5" />
        </button>
        <button
          class="bg-transparent border-none cursor-pointer p-2 rounded text-gray-500 text-base hover:bg-gray-100"
          @click="$emit('attach-file')"
        >
          <img src="../assets/editor/attach-file.svg" alt="文件" class="w-5 h-5" />
        </button>
      </div>
    </div>
    <div class="relative rounded-md">
      <textarea
        id="messageBox"
        ref="messageEditor"
        v-model="messageContent"
        class="w-full border-none outline-none p-1 pr-10 pb-12 text-sm font-inherit resize-none min-h-28 placeholder-gray-400 bg-transparent"
        placeholder="输入消息... (Enter发送，Ctrl+Enter换行)"
        rows="6"
        @keydown="handleKeyDown"
      ></textarea>
      <button
        id="sendBtn"
        class="absolute bottom-2 right-2 bg-[#214d91] text-white border-none px-4 py-2 rounded-md cursor-pointer text-sm font-medium hover:bg-[#2d6bc9] disabled:bg-[#b1c3e0] disabled:cursor-not-allowed"
        :disabled="!messageContent.trim() || isCurrentContactLoading"
        @click="handleSend"
      >
        <span v-if="isCurrentContactLoading" class="flex items-center gap-1">
          <div
            class="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"
          ></div>
          加载中
        </span>
        <span v-else>发送</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { User } from '../api'
import { notificationService } from '../services/notificationService'
import { useMessageStore } from '../store/message/index'

interface Contact {
  id: string
  name: string
  avatar: string
  status: string
  lastMessage: string
  lastMessageTime: Date
  user: User
}

interface Props {
  currentContact: Contact | null
}

defineProps<Props>()

const emit = defineEmits<{
  'send-message': [content: string]
  'insert-emoji': []
  'attach-file': []
  'screen-shot': []
}>()

// 状态管理
const messageStore = useMessageStore()

// 响应式数据
const messageContent = ref('')
const messageEditor = ref<HTMLTextAreaElement>()

// 计算属性：检查当前联系人是否正在加载
const isCurrentContactLoading = computed(() => {
  return messageStore.isCurrentContactLoading
})

const handleSend = () => {
  if (!messageContent.value.trim()) {
    notificationService.warning('请输入消息内容')
    return
  }

  // 检查消息长度
  const content = messageContent.value.replace(/^\s+|\s+$/g, '')
  if (content.length > 1000) {
    notificationService.warning('消息内容过长，请控制在1000字符以内')
    return
  }

  // 清空输入框
  messageContent.value = ''

  // 触发发送事件
  emit('send-message', content)
}

const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    if (event.ctrlKey) {
      // Ctrl+Enter: 插入换行符
      event.preventDefault()
      const textarea = event.target as HTMLTextAreaElement
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const value = textarea.value

      // 在光标位置插入换行符
      messageContent.value = value.substring(0, start) + '\n' + value.substring(end)

      // 设置光标位置到换行符后面
      setTimeout(() => {
        textarea.selectionStart = textarea.selectionEnd = start + 1
      }, 0)
    } else {
      // Enter: 发送消息
      event.preventDefault()
      handleSend()
    }
  }
}

// 暴露方法给父组件
defineExpose({
  focus: () => messageEditor.value?.focus(),
  clear: () => {
    messageContent.value = ''
  }
})
</script>
