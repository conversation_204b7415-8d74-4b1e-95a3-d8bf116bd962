// 消息状态管理 - 重构后的主Store
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { apiClient } from '../../api'
import { useWebSocketStore } from '../websocket'

// 导入类型定义
import type { Message, ChatSession } from './types'

// 导入各个模块
import { MessageHandler } from './message'
import { SessionManager } from './session'
import { CacheManager } from './cache'
import { wsManager } from '../../services/websocket'

export const useMessageStore = defineStore('message', () => {
  // 获取全局 WebSocket 状态
  const webSocketStore = useWebSocketStore()

  // 核心状态
  const messages = ref<Map<string, Message[]>>(new Map()) // 按用户ID分组的消息
  const chatSessions = ref<Map<string, ChatSession>>(new Map()) // 聊天会话
  const currentChatUserId = ref<string>('') // 当前聊天的用户ID
  const isLoading = ref(false)
  const error = ref<string>('')

  // 分页相关状态
  const paginationState = ref<
    Map<
      string,
      {
        currentPage: number
        hasMore: boolean
        isLoading: boolean
      }
    >
  >(new Map())

  // 初始化各个管理模块
  const messageHandler = new MessageHandler(messages, currentChatUserId, error)
  const sessionManager = new SessionManager(chatSessions, currentChatUserId, messages)
  const cacheManager = new CacheManager(messages, isLoading, error, paginationState)

  // 计算属性
  const currentMessages = computed(() => {
    if (!currentChatUserId.value) return []
    return messages.value.get(currentChatUserId.value) || []
  })

  // 当前联系人是否正在加载
  const isCurrentContactLoading = computed(() => {
    if (!currentChatUserId.value) return false
    return cacheManager.isUserLoading(currentChatUserId.value)
  })

  // 从SessionManager获取计算属性
  const sortedChatSessions = sessionManager.sortedChatSessions
  const totalUnreadCount = sessionManager.totalUnreadCount

  // 获取当前用户ID的辅助函数
  const getCurrentUserId = (): string => {
    const user = apiClient.getCurrentUser()
    return user?.id || ''
  }

  // 初始化WebSocket连接
  const initWebSocket = async (token?: string) => {
    try {
      // 先初始化数据库
      await cacheManager.initDatabase(getCurrentUserId)

      // 设置WebSocket事件监听
      wsManager.on('onMessage', (message) => {
        messageHandler
          .handleIncomingMessage(message, getCurrentUserId, (userId, msg) =>
            sessionManager.updateChatSession(userId, msg, getCurrentUserId)
          )
          .catch((error) => {
            console.error('处理接收消息失败:', error)
            error.value = '处理消息失败'
          })
      })

      wsManager.on('onSystemMessage', (message) => {
        messageHandler.handleIncomingSystemMessage(message)
      })

      wsManager.on('onError', (errorMsg) => {
        error.value = errorMsg.message
        console.error('WebSocket错误:', errorMsg)
      })

      // 连接WebSocket
      await wsManager.connect(token)
    } catch (err) {
      console.error('WebSocket连接失败:', err)
      error.value = err instanceof Error ? err.message : '连接失败'
      throw err
    }
  }

  // 发送消息
  const sendMessage = async (receiverId: string, content: string): Promise<boolean> => {
    return await messageHandler.sendMessage(receiverId, content, getCurrentUserId, (userId, msg) =>
      sessionManager.updateChatSession(userId, msg, getCurrentUserId)
    )
  }

  // 获取聊天历史
  const loadChatHistory = async (otherUserId: string, page = 1, limit = 50): Promise<boolean> => {
    return await cacheManager.loadChatHistory(
      otherUserId,
      page,
      limit,
      (userId, msg) => sessionManager.updateChatSession(userId, msg, getCurrentUserId),
      (userId) => sessionManager.createEmptySession(userId)
    )
  }

  // 加载更多历史消息
  const loadMoreHistory = async (otherUserId: string): Promise<boolean> => {
    return await cacheManager.loadMoreHistory(otherUserId, (userId, msg) =>
      sessionManager.updateChatSession(userId, msg, getCurrentUserId)
    )
  }

  // 设置当前聊天用户
  const setCurrentChatUser = (userId: string) => {
    sessionManager.setCurrentChatUser(userId, getCurrentUserId)
  }

  // 断开WebSocket连接
  const disconnectWebSocket = () => {
    wsManager.disconnect()
  }

  // 手动重连WebSocket
  const manualReconnectWebSocket = () => {
    wsManager.reconnect()
  }

  // 清除错误
  const clearError = () => {
    error.value = ''
  }

  // 清理WebSocket事件监听器
  const cleanupWebSocket = () => {
    wsManager.cleanup()
  }

  // 批量设置聊天会话（用于从API加载的联系人数据）
  const setChatSessions = (sessions: ChatSession[]) => {
    sessionManager.setChatSessions(sessions)
  }

  // 设置联系人信息（从登录后获取的联系人列表中设置）
  const setContactsInfo = (contacts: Array<{ id: string; user: any }>) => {
    sessionManager.setContactsInfo(contacts)
  }

  // 重试发送失败的消息
  const retryMessage = async (userId: string, messageId: string): Promise<boolean> => {
    return await messageHandler.retryMessage(userId, messageId)
  }

  // 获取连接状态信息
  const getConnectionInfo = () => {
    return wsManager.getConnectionInfo()
  }

  // 检查特定用户是否正在加载
  const isUserLoading = (userId: string): boolean => {
    return cacheManager.isUserLoading(userId)
  }

  // 获取当前正在加载的用户列表
  const getLoadingUsers = (): string[] => {
    return cacheManager.getLoadingUsers()
  }

  // 强制停止特定用户的加载状态
  const forceStopUserLoading = (userId: string) => {
    cacheManager.forceStopUserLoading(userId)
  }

  // 获取分页状态
  const getPaginationState = (userId: string) => {
    return paginationState.value.get(userId) || { currentPage: 1, hasMore: true, isLoading: false }
  }

  // 检查是否有更多历史消息
  const hasMoreHistory = (userId: string): boolean => {
    const state = paginationState.value.get(userId)
    return state ? state.hasMore : true
  }

  // 检查是否正在加载历史消息
  const isLoadingHistory = (userId: string): boolean => {
    const state = paginationState.value.get(userId)
    return state ? state.isLoading : false
  }

  // 清理资源
  const cleanup = () => {
    wsManager.cleanup()
    cacheManager.clearCache()
    sessionManager.clearAllSessions()
  }

  return {
    // 状态
    messages,
    chatSessions,
    currentChatUserId,
    wsState: webSocketStore.state, // 使用全局 WebSocket 状态
    isLoading,
    error,
    paginationState,

    // 计算属性
    currentMessages,
    isCurrentContactLoading,
    sortedChatSessions,
    totalUnreadCount,

    // 核心方法
    initWebSocket,
    sendMessage,
    loadChatHistory,
    loadMoreHistory,
    setCurrentChatUser,
    disconnectWebSocket,
    manualReconnectWebSocket,
    clearError,
    cleanupWebSocket,
    retryMessage,
    setChatSessions,
    setContactsInfo,

    // 辅助方法
    getConnectionInfo,
    isUserLoading,
    getLoadingUsers,
    forceStopUserLoading,
    getPaginationState,
    hasMoreHistory,
    isLoadingHistory,
    cleanup,

    // 暴露 WebSocket store 的方法
    webSocketStore,

    // 暴露各个管理模块（用于高级用法或测试）
    messageHandler,
    sessionManager,
    cacheManager
  }
})

// 重新导出类型，方便外部使用
export type { Message, ChatSession, ChatHistoryResponse } from './types'
