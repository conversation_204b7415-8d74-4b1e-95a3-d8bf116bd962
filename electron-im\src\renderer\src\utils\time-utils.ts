/**
 * 时间格式化工具函数
 */

/**
 * 格式化相对时间（多久之前）
 */
export const formatTime = (date: Date): string => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 1000 * 60) {
    return '刚刚'
  } else if (diff < 1000 * 60 * 60) {
    return `${Math.floor(diff / (1000 * 60))}分钟前`
  } else if (diff < 1000 * 60 * 60 * 24) {
    return `${Math.floor(diff / (1000 * 60 * 60))}小时前`
  } else {
    return date.toLocaleDateString()
  }
}

/**
 * 格式化时间戳为相对时间
 */
export const formatTimestamp = (timestamp: number | string): string => {
  const date = new Date(typeof timestamp === 'string' ? parseInt(timestamp) : timestamp)
  return formatTime(date)
}

/**
 * 格式化消息时间（完整时间）
 */
export const formatMessageTime = (date: Date): string => {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

/**
 * 标准化时间戳格式
 * 将字符串格式的时间戳（如 ISO 字符串）转换为数字格式的毫秒时间戳
 */
export const normalizeTimestamp = (timestamp: string | number): number => {
  if (typeof timestamp === 'number') {
    return timestamp
  }

  // 如果是字符串格式的时间，尝试解析
  if (typeof timestamp === 'string') {
    // 处理 ISO 格式的时间字符串
    const parsed = new Date(timestamp).getTime()

    // 检查解析结果是否有效
    if (isNaN(parsed)) {
      console.warn('无法解析时间戳:', timestamp)
      return Date.now() // 返回当前时间作为备用
    }

    return parsed
  }

  // 如果都不是，返回当前时间
  console.warn('无效的时间戳格式:', timestamp)
  return Date.now()
}
